# Gemini Grounding Mode Implementation Guide

This guide explains how to enable grounding mode with Google's Gemini models to access live internet information and provide up-to-date responses.

## Overview

Grounding mode allows <PERSON> to:
- Access live internet information through Google Search
- Provide factual, up-to-date responses
- Include source citations and links
- Reduce hallucinations by grounding responses in real data

## Implementation Approaches

### 1. For Gemini 2.0+ Models (Recommended for gemini-2.5-flash-preview-04-17)

For newer Gemini models, use **Search as a tool**:

#### Step 1: Install Required Dependencies

```bash
poetry add google-generativeai
```

#### Step 2: Implementation with <PERSON><PERSON><PERSON><PERSON>

```python
from langchain_google_genai import ChatGoogleGenerativeA<PERSON>
from pydantic import SecretStr

# Method 1: Using the google-generativeai package directly
try:
    from google.generativeai.types import Tool as GoogleTool
    from google.generativeai.types import GoogleSearchRetrieval
    
    def get_llm_with_grounding():
        llm = ChatGoogleGenerativeAI(
            model="gemini-2.5-flash-preview-04-17",
            api_key=SecretStr(config.gemini_api_key),
        )
        
        # Create Google Search tool
        google_search_tool = GoogleTool(
            google_search_retrieval=GoogleSearchRetrieval()
        )
        
        # Bind the search tool to the model
        return llm.bind_tools([google_search_tool])
        
except ImportError:
    print("google-generativeai package not available")
```

#### Step 3: Alternative Implementation (JavaScript-style for Python)

```python
# Alternative approach using the newer API structure
def get_llm_with_grounding_v2():
    from google.generativeai.types import Tool
    
    llm = ChatGoogleGenerativeAI(
        model="gemini-2.5-flash-preview-04-17",
        api_key=SecretStr(config.gemini_api_key),
    )
    
    # Configure search grounding tool
    search_tool = {
        "google_search_retrieval": {
            "dynamic_retrieval_config": {
                "mode": "MODE_DYNAMIC",
                "dynamic_threshold": 0.7  # Adjust threshold as needed
            }
        }
    }
    
    return llm.bind_tools([search_tool])
```

### 2. For Gemini 1.5 Models (Legacy Approach)

For Gemini 1.5 models, use the built-in Google Search Retrieval:

```python
def get_llm_with_grounding_legacy():
    from google.generativeai.types import GoogleSearchRetrievalTool
    
    llm = ChatGoogleGenerativeAI(
        model="gemini-1.5-pro",
        api_key=SecretStr(config.gemini_api_key),
    )
    
    # Configure grounding with Google Search
    search_retrieval_tool = GoogleSearchRetrievalTool(
        dynamic_retrieval_config={
            "mode": "MODE_DYNAMIC",
            "dynamic_threshold": 0.3
        }
    )
    
    return llm.bind_tools([search_retrieval_tool])
```

### 3. Using Direct API Configuration

```python
def get_llm_with_grounding_direct():
    llm = ChatGoogleGenerativeAI(
        model="gemini-2.5-flash-preview-04-17",
        api_key=SecretStr(config.gemini_api_key),
        # Configure grounding directly in model initialization
        tools=[{
            "google_search_retrieval": {
                "dynamic_retrieval_config": {
                    "mode": "MODE_DYNAMIC",
                    "dynamic_threshold": 0.7
                }
            }
        }]
    )
    
    return llm
```

## Configuration Options

### Dynamic Retrieval Threshold

The `dynamic_threshold` parameter controls when grounding is triggered:

- **0.0**: Always use grounding
- **0.3**: Default threshold (recommended)
- **0.7**: Higher threshold (less frequent grounding)
- **1.0**: Never use grounding

### Example Threshold Behavior

| Query Type | Prediction Score | Threshold 0.3 | Threshold 0.7 |
|------------|------------------|---------------|---------------|
| "Write a poem" | 0.13 | No grounding | No grounding |
| "Recipe suggestion" | 0.36 | Grounding | No grounding |
| "Latest F1 winner" | 0.97 | Grounding | Grounding |

## Usage Examples

### Basic Usage

```python
# Get grounded LLM
grounded_llm = get_llm_with_grounding()

# Ask a question that benefits from live data
response = grounded_llm.invoke("Who won the 2024 World Series?")
print(response.content)

# Check grounding metadata
if hasattr(response, 'response_metadata') and 'groundingMetadata' in response.response_metadata:
    grounding_data = response.response_metadata['groundingMetadata']
    print("Sources:", grounding_data.get('groundingChunks', []))
```

### Integration with Your Current Code

```python
def call_model_with_grounding(state: ConversationState, tools: list[StructuredTool]) -> dict:
    messages = state["messages"]
    account_info = state.get("account_info")

    llm_messages: list[BaseMessage] = [
        SystemMessage(content=AgentPrompts.get_sales_assistant_system_prompt())
    ]

    if account_info:
        account_context_content = AgentPrompts.format_account_context_message(account_info)
        llm_messages.append(SystemMessage(content=account_context_content))

    llm_messages.extend(messages)
    
    # Use grounded LLM for better factual accuracy
    llm_with_tools = get_llm_with_grounding().bind_tools(tools)
    response = llm_with_tools.invoke(llm_messages)

    ai_response = typing.cast("AIMessage", response)
    return {"messages": [ai_response]}
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure `google-generativeai` package is installed
2. **API Key Issues**: Verify your Google AI API key is valid
3. **Model Compatibility**: Grounding works differently for 1.5 vs 2.0+ models
4. **Rate Limits**: Grounding may increase API usage

### Fallback Strategy

```python
def get_llm_with_grounding_safe():
    """Safe implementation with fallback to regular LLM."""
    try:
        return get_llm_with_grounding()
    except Exception as e:
        print(f"Grounding setup failed: {e}")
        return get_llm()  # Fallback to regular LLM
```

## Next Steps

1. Install the `google-generativeai` package
2. Test the grounding functionality with simple queries
3. Integrate into your existing `call_model` function
4. Monitor API usage and adjust thresholds as needed
5. Implement proper error handling and fallbacks
