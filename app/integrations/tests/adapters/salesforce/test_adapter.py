import pytest

from app.integrations.adapters.salesforce.adapter import SalesforceAdapter
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_credentials(mocker):
    mock_creds = mocker.Mock(spec=ICredentials)
    mock_creds.secrets = {
        "username": "test_username",
        "password": "test_password",
        "security_token": "test_token",
    }
    return mock_creds


@pytest.fixture(autouse=True)
def mock_salesforce_handler(mocker):
    mock_handler = mocker.MagicMock()
    mock_handler.get_opportunity.return_value = {
        "Id": "001",
        "Name": "Test Opportunity",
    }
    mock_handler.update_opportunity.return_value = {
        "Id": "001",
        "Name": "Updated Opportunity",
    }
    mock_handler.list_opportunities_by_account.return_value = [
        {"Id": "001", "Name": "Test Opportunity"}
    ]
    mock_handler.get_account.return_value = {"Id": "002", "Name": "Test Account"}

    # Create proper CRMAccountAccessData objects for resolve_account_access
    from app.integrations.schemas import CRMAccountAccessData

    mock_handler.resolve_account_access.return_value = [
        CRMAccountAccessData(
            account_id="002",
            account_name="Test Account",
            access_type="owner",
            access_role=None,
        )
    ]

    handler_class = mocker.patch(
        "app.integrations.adapters.salesforce.adapter.SalesforceHandler"
    )
    handler_class.return_value = mock_handler
    return mock_handler


@pytest.fixture
def salesforce_adapter(mock_credentials):
    return SalesforceAdapter(credentials=mock_credentials)


def test_init(mocker, mock_credentials):
    handler_class = mocker.patch(
        "app.integrations.adapters.salesforce.adapter.SalesforceHandler"
    )
    adapter = SalesforceAdapter(credentials=mock_credentials)
    assert adapter.credentials == mock_credentials
    handler_class.assert_called_once_with(credentials=mock_credentials)


def test_source(salesforce_adapter):
    assert salesforce_adapter.source == IntegrationSource.SALESFORCE


def test_get_opportunity(salesforce_adapter, mock_salesforce_handler):
    opportunity_id = "opp123"
    result = salesforce_adapter.get_opportunity(opportunity_id)
    mock_salesforce_handler.get_opportunity.assert_called_once_with(opportunity_id)
    assert result == {"Id": "001", "Name": "Test Opportunity"}


def test_update_opportunity(salesforce_adapter, mock_salesforce_handler):
    opportunity_id = "opp123"
    fields = {"Name": "New Name"}
    result = salesforce_adapter.update_opportunity(opportunity_id, fields)
    mock_salesforce_handler.update_opportunity.assert_called_once_with(
        opportunity_id, fields
    )
    assert result == {"Id": "001", "Name": "Updated Opportunity"}


def test_list_opportunities_by_account(salesforce_adapter, mock_salesforce_handler):
    account_id = "account456"
    limit = 50
    offset = 10
    result = salesforce_adapter.list_opportunities_by_account(
        account_id=account_id,
        limit=limit,
        offset=offset,
    )
    mock_salesforce_handler.list_opportunities_by_account.assert_called_once_with(
        account_id=account_id,
        limit=limit,
        offset=offset,
    )
    assert len(result) == 1
    assert result[0]["Id"] == "001"
    assert result[0]["Name"] == "Test Opportunity"


def test_get_account(salesforce_adapter, mock_salesforce_handler):
    account_id = "acc123"
    result = salesforce_adapter.get_account(account_id)
    mock_salesforce_handler.get_account.assert_called_once_with(account_id)
    assert result == {"Id": "002", "Name": "Test Account"}


def test_resolve_account_access(salesforce_adapter, mock_salesforce_handler):
    user_id = "user123"
    result = salesforce_adapter.resolve_account_access(user_id)
    mock_salesforce_handler.resolve_account_access.assert_called_once_with(
        salesforce_user_id=user_id
    )
    assert len(result) == 1
    assert result[0].account_id == "002"
    assert result[0].account_name == "Test Account"
    assert result[0].access_type == "owner"
    assert result[0].access_role is None
