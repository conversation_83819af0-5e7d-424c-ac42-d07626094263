import asyncio
import inspect
from collections.abc import Awaitable, Callable
from typing import Any
from uuid import UUID

from langchain_linkup import Linkup<PERSON>earchTool
from pydantic import BaseModel

from app.agentic.context.schemas import (
    GetAccount,
    GetOpportunity,
    ListOpportunitiesByAccount,
    ToolDefinition,
    UpdateOpportunity,
)
from app.workspace.integrations.user_integrations import UserIntegrations


def get_tools(
    user_id: UUID,
    user_integrations: UserIntegrations,
) -> list[ToolDefinition]:
    tools: list[ToolDefinition] = []

    crm = user_integrations.crm()

    if not crm:
        raise RuntimeError(f"No CRM integration configured for user {user_id}")

    linkup_tool = LinkupSearchTool(
        depth="standard",
        output_type="searchResults",
        linkup_api_key="cf0d2c4d-9823-4494-ad80-eda9fb9a7186",
    )

    async def search_web(query: str) -> str:
        result = linkup_tool.invoke({"query": query})

        if hasattr(result, "results") and result.results:
            search_results = []
            for item in result.results:
                search_results.append(
                    f"Title: {item.name}\nURL: {item.url}\nContent: {item.content}\n"
                )
            return "\n\n".join(search_results)
        return "No search results found."

    tools.append(
        ToolDefinition(
            name="search_web",
            coroutine=search_web,
            description=linkup_tool.description,
            args_schema=linkup_tool.args_schema,
        )
    )

    crm_methods: list[tuple[str, str, type[BaseModel]]] = [
        ("get_opportunity", "Fetch a CRM opportunity by its ID", GetOpportunity),
        (
            "update_opportunity",
            "Update a CRM opportunity with provided fields",
            UpdateOpportunity,
        ),
        (
            "list_opportunities_by_account",
            "List CRM opportunities for a given account",
            ListOpportunitiesByAccount,
        ),
        ("get_account", "Fetch a CRM account by its ID", GetAccount),
    ]

    for name, description, schema in crm_methods:
        method = getattr(crm, name)
        coroutine = _ensure_async(method)
        tools.append(
            ToolDefinition(
                name=name,
                coroutine=coroutine,
                description=description,
                args_schema=schema,
            )
        )

    return tools


def _ensure_async(fn: Callable[..., Any]) -> Callable[..., Awaitable[Any]]:
    if inspect.iscoroutinefunction(fn):
        return fn

    async def wrapper(*args: Any, **kwargs: Any) -> Any:
        return await asyncio.to_thread(fn, *args, **kwargs)

    return wrapper
